# DataTypeInput 组件

## 概述

DataTypeInput 是一个根据 `dataType` 类型自动切换不同输入组件的独立组件，用于替换 ValueDialog 中 `data.type === 'text'` 时的 textarea。

## 功能特性

### 支持的数据类型

根据 `VALUE_TYPE_MAP` 常量定义，组件支持以下数据类型：

- **string** (文本) - 使用 `t-textarea` 组件
- **decimal/double/float** (数值) - 使用 `t-input-number` 组件，支持小数
- **int/short/long** (整数) - 使用 `t-input-number` 组件，不支持小数
- **DateTime** (日期时间) - 使用 `t-date-picker` 组件，支持时间选择
- **bool** (布尔值) - 使用 `t-switch` 组件
- **char** (字符) - 使用 `t-input` 组件，限制最大长度为1
- **byte[]** (二进制数据) - 使用 `t-upload` 组件
- **object** (字典) - 使用**结构化编辑器**，左侧可视化编辑，右侧JSON预览
- **array** (数组) - 使用**结构化编辑器**，左侧可视化编辑，右侧JSON预览

### 组件属性

```typescript
interface Props {
  modelValue?: any;        // 输入值
  dataType?: string;       // 数据类型，对应 VALUE_TYPE_MAP 的键
  placeholder?: string;    // 占位符文本
}
```

### 组件事件

- `update:modelValue` - 值变化时触发
- `change` - 值变化时触发

## 使用方式

### 在 ValueDialog 中的集成

```vue
<data-type-input
  v-show="data.type === 'text'"
  v-model="data.textValue"
  :data-type="data.dataType"
  placeholder="请输入文本"
/>
```

### 在 ValueInput 中使用 dataType 属性

```vue
<!-- 数值类型示例 -->
<value-input
  v-model:data-value="formData.expirationSeconds"
  data-type="int"
  placeholder="请输入过期时间（秒）"
/>

<!-- 日期时间类型示例 -->
<value-input
  v-model:data-value="formData.nextBeginTime"
  data-type="DateTime"
  placeholder="请选择下次开始时间"
/>

<!-- 布尔值类型示例 -->
<value-input
  v-model:data-value="formData.isEnabled"
  data-type="bool"
  placeholder="是否启用"
/>

<!-- 对象类型示例 -->
<value-input
  v-model:data-value="formData.configObject"
  data-type="object"
  placeholder="请输入配置对象（JSON格式）"
/>
```

### 独立使用

```vue
<template>
  <data-type-input
    v-model="inputValue"
    :data-type="selectedDataType"
    placeholder="请输入内容"
    @change="onValueChange"
  />
</template>

<script setup>
import DataTypeInput from '@/components/action-panel/DataTypeInput.vue';

const inputValue = ref('');
const selectedDataType = ref('string');

const onValueChange = (value) => {
  console.log('Value changed:', value);
};
</script>
```

## 特殊功能

### 结构化编辑器（对象和数组类型）

对于 `object` 和 `array` 类型，组件提供了友好的结构化编辑器：

#### 数组编辑器特性
- **可视化列表编辑**: 左侧显示数组项列表，每项有索引标识
- **丰富类型支持**: 支持文本、数字、布尔值、**对象、数组、日期时间**等类型
- **嵌套编辑**: 对象和数组类型支持嵌套编辑，可以无限层级嵌套
- **添加/删除**: 可以动态添加和删除数组项
- **实时预览**: 右侧实时显示JSON格式预览
- **一键复制**: 可以复制生成的JSON到剪贴板

#### 对象编辑器特性
- **键值对编辑**: 左侧显示属性列表，可编辑键名和值
- **丰富类型支持**: 支持文本、数字、布尔值、**对象、数组、日期时间**等类型
- **嵌套编辑**: 对象和数组类型支持嵌套编辑，可以无限层级嵌套
- **添加/删除**: 可以动态添加和删除属性
- **实时预览**: 右侧实时显示JSON格式预览
- **一键复制**: 可以复制生成的JSON到剪贴板

#### 嵌套编辑器特性

**支持的嵌套类型**:
- **对象 (object)**: 可以包含其他对象、数组、基础类型
- **数组 (array)**: 可以包含其他数组、对象、基础类型
- **日期时间 (DateTime)**: 使用日期时间选择器

**嵌套编辑流程**:
1. 选择类型为"对象"或"数组"
2. 点击"编辑对象/数组"按钮
3. 在弹出的对话框中进行嵌套编辑
4. 支持无限层级嵌套

**示例嵌套结构**:
```json
{
  "user": {
    "name": "张三",
    "age": 25,
    "hobbies": ["读书", "游泳"],
    "address": {
      "city": "北京",
      "district": "朝阳区"
    }
  },
  "orders": [
    {
      "id": 1,
      "date": "2024-01-15T10:30:00",
      "items": ["商品A", "商品B"]
    }
  ]
}
```

#### 界面布局
```
┌─────────────────┬─────────────────┐
│   可视化编辑器   │   JSON 编辑器   │
│                │                │
│ ┌─────────────┐ │ ┌─────────────┐ │
│ │ 添加项/属性  │ │ │格式化│复制  │ │
│ └─────────────┘ │ └─────────────┘ │
│                │                │
│ [0] 对象 [编辑] │ {              │
│ [1] 数组 [编辑] │   "obj": {...} │
│ [2] 时间 选择器  │   "arr": [...] │
│                │ }              │
│                │ ↕ 可直接编辑    │
└─────────────────┴─────────────────┘
```

### JSON 验证

对于传统的JSON输入模式，组件会：
- 自动验证 JSON 格式
- 显示格式错误提示
- 确保对象类型不是数组，数组类型是数组

### 数值类型配置

不同数值类型有不同的配置：
- **decimal/double/float**: 支持2位小数
- **int/short/long**: 不支持小数
- **short**: 范围 -32768 到 32767
- **int**: 范围 -2147483648 到 2147483647
- **long**: 使用 JavaScript 安全整数范围

### 默认值处理

组件会根据数据类型自动设置合适的默认值：
- **bool**: false
- **数值类型**: 0
- **object**: {}
- **array**: []
- **DateTime**: 当前时间的 ISO 字符串
- **其他**: 空字符串

## 调试功能

在 ValueDialog 中，当 `data.type === 'text'` 时，会显示调试信息：
- 当前 dataType 值
- 数据类型选择器，可以手动切换类型进行测试

## 测试

创建了测试页面 `/system/test/data-type-input` 用于测试组件的各种功能。

## 文件结构

```
web/src/components/action-panel/
├── DataTypeInput.vue          # 主组件文件
├── StructuredEditor.vue       # 独立的结构化编辑器组件（新增）
├── ValueDialog.vue            # 集成了 DataTypeInput 的对话框
├── constants.ts               # VALUE_TYPE_MAP 常量定义
└── DataTypeInput_README.md    # 本文档

web/src/pages/test/
└── DataTypeInputTest.vue      # 测试页面
```

## 如何在现有Action中应用

### 修改步骤

1. **识别需要特定数据类型的字段**
   - 数值字段：过期时间、延迟时间、端口号等
   - 日期时间字段：开始时间、结束时间等
   - 布尔值字段：是否启用、是否必填等
   - 对象/数组字段：配置对象、参数列表等

2. **在 ValueInput 组件上添加 data-type 属性**
   ```vue
   <!-- 修改前 -->
   <value-input v-model:data-value="formData.timeout" />

   <!-- 修改后 -->
   <value-input
     v-model:data-value="formData.timeout"
     data-type="int"
     placeholder="请输入超时时间（毫秒）"
   />
   ```

3. **已修改的示例**
   - `CacheWrite/index.vue` - 过期时间字段使用 `int` 类型
   - `Delay/index.vue` - 延迟时间字段使用 `int` 类型
   - `JobChangeNextBeginTime/index.vue` - 时间字段使用 `DateTime` 类型
   - `ApiRequest/index.vue` - 请求头、请求参数、请求体使用 `object` 类型

### 推荐的数据类型映射

- **时间相关**: `DateTime`
- **整数**: `int` (常用) / `short` (小范围) / `long` (大范围)
- **小数**: `decimal` (精确) / `double` / `float`
- **开关**: `bool`
- **单字符**: `char`
- **配置对象**: `object`
- **列表数据**: `array`
- **文件上传**: `byte[]`
- **默认文本**: `string`

## 组件架构

### StructuredEditor.vue（独立组件）

专门处理对象和数组类型的结构化编辑器，具有以下特性：
- **独立性**: 可以单独使用，不依赖 DataTypeInput
- **专业性**: 专注于结构化数据编辑
- **可复用性**: 可以在其他地方复用
- **调试友好**: 包含详细的控制台日志

#### 使用方式
```vue
<structured-editor
  v-model="objectValue"
  data-type="object"
  @change="onValueChange"
/>

<structured-editor
  v-model="arrayValue"
  data-type="array"
  @change="onValueChange"
/>
```

### DataTypeInput.vue（主组件）

现在更加简洁，专注于类型分发：
- 对于简单类型（string、number、bool等）：直接处理
- 对于复杂类型（object、array）：委托给 StructuredEditor

## 修复的问题

1. **按钮点击无效**: 修复了对象编辑器中按钮点击事件不响应的问题
2. **代码重构**: 将结构化编辑器抽取为独立组件，提高代码可维护性
3. **调试增强**: 添加了详细的控制台日志，便于调试
4. **类型安全**: 改进了 TypeScript 类型定义

## 注意事项

1. 组件依赖 TDesign Vue Next 组件库
2. 文件上传功能需要配置正确的上传接口
3. JSON 验证使用延迟验证，避免输入过程中频繁报错
4. 数据类型变化时会自动重置值为默认值
5. **重要**: 需要在 ValueInput 组件上指定 `data-type` 属性才能使用对应的输入组件
6. **新增**: StructuredEditor 组件可以独立使用，适合需要专门编辑结构化数据的场景
