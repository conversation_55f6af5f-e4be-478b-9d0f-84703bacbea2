<template>
  <div class="data-type-input">
    <!-- 文本类型 -->
    <t-textarea
      v-if="isTextType"
      v-model="inputValue"
      :placeholder="placeholder"
      :autosize="{ minRows: 5, maxRows: 12 }"
      @change="onInputChange"
    />

    <!-- 数值类型 -->
    <t-input-number
      v-else-if="isNumberType"
      v-model="inputValue"
      :placeholder="placeholder"
      :decimal-places="getDecimalPlaces()"
      :min="getMinValue()"
      :max="getMaxValue()"
      @change="onInputChange"
    />

    <!-- 日期时间类型 -->
    <t-date-picker
      v-else-if="isDateTimeType"
      v-model="inputValue"
      :placeholder="placeholder"
      enable-time-picker
      format="YYYY-MM-DD HH:mm:ss"
      @change="onInputChange"
    />

    <!-- 布尔值类型 -->
    <t-switch v-else-if="isBoolType" v-model="inputValue" @change="onInputChange" />

    <!-- 字符类型 -->
    <t-input
      v-else-if="isCharType"
      v-model="inputValue"
      :placeholder="placeholder"
      :maxlength="1"
      @change="onInputChange"
    />

    <!-- 二进制数据类型 -->
    <t-upload
      v-else-if="isBinaryType"
      v-model="fileList"
      :action="uploadAction"
      :before-upload="beforeUpload"
      :on-success="onUploadSuccess"
      :on-error="onUploadError"
      theme="file"
      :placeholder="placeholder || '选择文件'"
      :tips="'支持上传二进制文件'"
    />

    <!-- 对象/数组类型 -->
    <div v-else-if="isObjectOrArrayType" class="structured-editor-container">
      <div class="editor-layout">
        <!-- 左侧：可视化编辑器 -->
        <div class="visual-editor">
          <!-- 数组编辑器 -->
          <div v-if="props.dataType === 'array'" class="array-editor">
            <div class="editor-header">
              <span class="editor-title">数组编辑器</span>
              <t-button size="small" theme="primary" @click="addArrayItem">
                <template #icon><add-icon /></template>
                添加项
              </t-button>
            </div>
            <div class="array-items">
              <div v-for="(item, index) in arrayItems" :key="index" class="array-item">
                <div class="item-index">{{ index }}</div>
                <div class="item-input">
                  <t-select
                    v-model="item.type"
                    :options="valueTypeOptions"
                    size="small"
                    style="width: 100px; margin-right: 8px"
                    @change="onArrayItemTypeChange(index)"
                  />
                  <t-input
                    v-if="item.type === 'string'"
                    v-model="item.value"
                    placeholder="请输入文本"
                    @change="updateArrayValue"
                  />
                  <t-input-number
                    v-else-if="item.type === 'number'"
                    v-model="item.value"
                    placeholder="请输入数字"
                    @change="updateArrayValue"
                  />
                  <t-switch v-else-if="item.type === 'boolean'" v-model="item.value" @change="updateArrayValue" />
                  <t-input v-else v-model="item.value" placeholder="请输入值" @change="updateArrayValue" />
                </div>
                <t-button size="small" theme="danger" variant="text" @click="removeArrayItem(index)">
                  <template #icon><delete-icon /></template>
                </t-button>
              </div>
              <div v-if="arrayItems.length === 0" class="empty-state">
                <t-button variant="dashed" block @click="addArrayItem"> 点击添加第一个数组项 </t-button>
              </div>
            </div>
          </div>

          <!-- 对象编辑器 -->
          <div v-else class="object-editor">
            <div class="editor-header">
              <span class="editor-title">对象编辑器</span>
              <t-button size="small" theme="primary" @click="addObjectProperty">
                <template #icon><add-icon /></template>
                添加属性
              </t-button>
            </div>
            <div class="object-properties">
              <div v-for="(prop, index) in objectProperties" :key="index" class="object-property">
                <div class="property-key">
                  <t-input v-model="prop.key" placeholder="属性名" size="small" @change="updateObjectValue" />
                </div>
                <div class="property-value">
                  <t-select
                    v-model="prop.type"
                    :options="valueTypeOptions"
                    size="small"
                    style="width: 100px; margin-right: 8px"
                    @change="onObjectPropertyTypeChange(index)"
                  />
                  <t-input
                    v-if="prop.type === 'string'"
                    v-model="prop.value"
                    placeholder="请输入文本"
                    @change="updateObjectValue"
                  />
                  <t-input-number
                    v-else-if="prop.type === 'number'"
                    v-model="prop.value"
                    placeholder="请输入数字"
                    @change="updateObjectValue"
                  />
                  <t-switch v-else-if="prop.type === 'boolean'" v-model="prop.value" @change="updateObjectValue" />
                  <t-input v-else v-model="prop.value" placeholder="请输入值" @change="updateObjectValue" />
                </div>
                <t-button size="small" theme="danger" variant="text" @click="removeObjectProperty(index)">
                  <template #icon><delete-icon /></template>
                </t-button>
              </div>
              <div v-if="objectProperties.length === 0" class="empty-state">
                <t-button variant="dashed" block @click="addObjectProperty"> 点击添加第一个属性 </t-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧：JSON预览 -->
        <div class="json-preview">
          <div class="preview-header">
            <span class="preview-title">JSON 预览</span>
            <t-button size="small" variant="text" @click="copyJsonToClipboard">
              <template #icon><copy-icon /></template>
              复制
            </t-button>
          </div>
          <div class="preview-content">
            <pre class="json-display">{{ formattedJson }}</pre>
          </div>
          <div v-if="jsonError" class="json-error">
            <t-alert theme="error" :message="jsonError" />
          </div>
        </div>
      </div>
    </div>

    <!-- 默认文本输入 -->
    <t-textarea
      v-else
      v-model="inputValue"
      :placeholder="placeholder"
      :autosize="{ minRows: 5, maxRows: 12 }"
      @change="onInputChange"
    />
  </div>
</template>

<script lang="ts">
export default {
  name: 'DataTypeInput',
};
</script>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { AddIcon, DeleteIcon, CopyIcon } from 'tdesign-icons-vue-next';
import { VALUE_TYPE_MAP } from './constants';

interface Props {
  modelValue?: any;
  dataType?: string;
  placeholder?: string;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  dataType: 'string',
  placeholder: '请输入内容',
});

const emits = defineEmits(['update:modelValue', 'change']);

// 内部值
const inputValue = ref(props.modelValue);
const jsonString = ref('');
const jsonError = ref('');
const fileList = ref([]);

// 结构化编辑器相关
const arrayItems = ref([]);
const objectProperties = ref([]);

// 值类型选项
const valueTypeOptions = [
  { label: '文本', value: 'string' },
  { label: '数字', value: 'number' },
  { label: '布尔值', value: 'boolean' },
  { label: '对象', value: 'object' },
  { label: '数组', value: 'array' },
];

// 类型判断
const isTextType = computed(() => {
  return ['string'].includes(props.dataType);
});

const isNumberType = computed(() => {
  return ['decimal', 'int', 'short', 'long', 'double', 'float'].includes(props.dataType);
});

const isDateTimeType = computed(() => {
  return props.dataType === 'DateTime';
});

const isBoolType = computed(() => {
  return props.dataType === 'bool';
});

const isCharType = computed(() => {
  return props.dataType === 'char';
});

const isBinaryType = computed(() => {
  return props.dataType === 'byte[]';
});

const isObjectOrArrayType = computed(() => {
  return ['object', 'array'].includes(props.dataType);
});

// 数值类型配置
const getDecimalPlaces = () => {
  switch (props.dataType) {
    case 'decimal':
    case 'double':
    case 'float':
      return 2;
    default:
      return 0;
  }
};

const getMinValue = () => {
  switch (props.dataType) {
    case 'short':
      return -32768;
    case 'int':
      return -2147483648;
    case 'long':
      return Number.MIN_SAFE_INTEGER;
    default:
      return undefined;
  }
};

const getMaxValue = () => {
  switch (props.dataType) {
    case 'short':
      return 32767;
    case 'int':
      return 2147483647;
    case 'long':
      return Number.MAX_SAFE_INTEGER;
    default:
      return undefined;
  }
};

// JSON 相关
const getJsonPlaceholder = () => {
  if (props.dataType === 'object') {
    return '请输入JSON对象，例如：\n{\n  "key": "value",\n  "number": 123\n}';
  } else if (props.dataType === 'array') {
    return '请输入JSON数组，例如：\n[\n  "item1",\n  "item2",\n  123\n]';
  }
  return '请输入JSON格式数据';
};

const validateJson = () => {
  if (!jsonString.value.trim()) {
    jsonError.value = '';
    return;
  }

  try {
    const parsed = JSON.parse(jsonString.value);

    if (props.dataType === 'object' && (typeof parsed !== 'object' || Array.isArray(parsed))) {
      jsonError.value = '请输入有效的JSON对象';
      return;
    }

    if (props.dataType === 'array' && !Array.isArray(parsed)) {
      jsonError.value = '请输入有效的JSON数组';
      return;
    }

    jsonError.value = '';
    inputValue.value = parsed;
    onInputChange();
  } catch (error) {
    jsonError.value = 'JSON格式错误，请检查语法';
  }
};

const onJsonChange = () => {
  // 延迟验证，避免输入过程中频繁报错
  setTimeout(validateJson, 500);
};

// 文件上传相关
const uploadAction = '/api/upload'; // 根据实际情况修改
const beforeUpload = (file: File) => {
  // 可以在这里添加文件类型和大小验证
  return true;
};

const onUploadSuccess = (response: any) => {
  // 处理上传成功
  inputValue.value = response.data; // 假设返回文件路径或base64
  onInputChange();
  MessagePlugin.success('文件上传成功');
};

const onUploadError = (error: any) => {
  MessagePlugin.error('文件上传失败');
  console.error('Upload error:', error);
};

// 输入变化处理
const onInputChange = () => {
  emits('update:modelValue', inputValue.value);
  emits('change', inputValue.value);
};

// 初始化结构化编辑器
const initializeStructuredEditor = (value: any) => {
  if (props.dataType === 'array' && Array.isArray(value)) {
    arrayItems.value = value.map((item) => ({
      type: typeof item === 'number' ? 'number' : typeof item === 'boolean' ? 'boolean' : 'string',
      value: item,
    }));
  } else if (props.dataType === 'object' && typeof value === 'object' && value !== null) {
    objectProperties.value = Object.entries(value).map(([key, val]) => ({
      key,
      type: typeof val === 'number' ? 'number' : typeof val === 'boolean' ? 'boolean' : 'string',
      value: val,
    }));
  }
};

// 监听外部值变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (isObjectOrArrayType.value && typeof newValue === 'object') {
      jsonString.value = JSON.stringify(newValue, null, 2);
      inputValue.value = newValue;
      initializeStructuredEditor(newValue);
    } else {
      inputValue.value = newValue;
    }
  },
  { immediate: true },
);

// 监听数据类型变化，重置值
watch(
  () => props.dataType,
  () => {
    inputValue.value = getDefaultValue();
    jsonString.value = '';
    jsonError.value = '';
    onInputChange();
  },
);

// 获取默认值
const getDefaultValue = () => {
  switch (props.dataType) {
    case 'bool':
      return false;
    case 'int':
    case 'short':
    case 'long':
    case 'decimal':
    case 'double':
    case 'float':
      return 0;
    case 'object':
      return {};
    case 'array':
      return [];
    case 'DateTime':
      return new Date().toISOString();
    default:
      return '';
  }
};

// 数组编辑器方法
const addArrayItem = () => {
  arrayItems.value.push({
    type: 'string',
    value: '',
  });
  updateArrayValue();
};

const removeArrayItem = (index: number) => {
  arrayItems.value.splice(index, 1);
  updateArrayValue();
};

const onArrayItemTypeChange = (index: number) => {
  const item = arrayItems.value[index];
  // 根据类型重置值
  switch (item.type) {
    case 'string':
      item.value = '';
      break;
    case 'number':
      item.value = 0;
      break;
    case 'boolean':
      item.value = false;
      break;
    default:
      item.value = '';
  }
  updateArrayValue();
};

const updateArrayValue = () => {
  const result = arrayItems.value.map((item) => {
    if (item.type === 'number') {
      return Number(item.value) || 0;
    } else if (item.type === 'boolean') {
      return Boolean(item.value);
    }
    return item.value;
  });
  inputValue.value = result;
  onInputChange();
};

// 对象编辑器方法
const addObjectProperty = () => {
  objectProperties.value.push({
    key: '',
    type: 'string',
    value: '',
  });
  updateObjectValue();
};

const removeObjectProperty = (index: number) => {
  objectProperties.value.splice(index, 1);
  updateObjectValue();
};

const onObjectPropertyTypeChange = (index: number) => {
  const prop = objectProperties.value[index];
  // 根据类型重置值
  switch (prop.type) {
    case 'string':
      prop.value = '';
      break;
    case 'number':
      prop.value = 0;
      break;
    case 'boolean':
      prop.value = false;
      break;
    default:
      prop.value = '';
  }
  updateObjectValue();
};

const updateObjectValue = () => {
  const result = {};
  objectProperties.value.forEach((prop) => {
    if (prop.key) {
      if (prop.type === 'number') {
        result[prop.key] = Number(prop.value) || 0;
      } else if (prop.type === 'boolean') {
        result[prop.key] = Boolean(prop.value);
      } else {
        result[prop.key] = prop.value;
      }
    }
  });
  inputValue.value = result;
  onInputChange();
};

// JSON预览相关
const formattedJson = computed(() => {
  try {
    return JSON.stringify(inputValue.value, null, 2);
  } catch (error) {
    return '无效的JSON';
  }
});

const copyJsonToClipboard = async () => {
  try {
    await navigator.clipboard.writeText(formattedJson.value);
    MessagePlugin.success('JSON已复制到剪贴板');
  } catch (error) {
    MessagePlugin.error('复制失败');
  }
};
</script>

<style lang="less" scoped>
.data-type-input {
  width: 100%;

  .json-editor-container {
    .json-error {
      margin-top: 8px;
    }
  }

  .structured-editor-container {
    .editor-layout {
      display: flex;
      gap: 16px;
      min-height: 300px;

      .visual-editor {
        flex: 1;
        border: 1px solid var(--td-border-level-1-color);
        border-radius: 6px;
        padding: 12px;
        background-color: var(--td-bg-color-container);

        .editor-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;
          padding-bottom: 8px;
          border-bottom: 1px solid var(--td-border-level-2-color);

          .editor-title {
            font-weight: 500;
            color: var(--td-text-color-primary);
          }
        }

        .array-items,
        .object-properties {
          max-height: 250px;
          overflow-y: auto;

          .array-item,
          .object-property {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
            padding: 8px;
            background-color: var(--td-bg-color-container-hover);
            border-radius: 4px;
            border: 1px solid var(--td-border-level-2-color);

            .item-index {
              min-width: 24px;
              height: 24px;
              display: flex;
              align-items: center;
              justify-content: center;
              background-color: var(--td-brand-color);
              color: white;
              border-radius: 50%;
              font-size: 12px;
              font-weight: 500;
            }

            .item-input,
            .property-value {
              flex: 1;
              display: flex;
              align-items: center;
              gap: 8px;
            }

            .property-key {
              min-width: 120px;
            }
          }

          .empty-state {
            text-align: center;
            padding: 20px;
            color: var(--td-text-color-placeholder);
          }
        }
      }

      .json-preview {
        flex: 1;
        border: 1px solid var(--td-border-level-1-color);
        border-radius: 6px;
        background-color: var(--td-bg-color-container);
        display: flex;
        flex-direction: column;

        .preview-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px;
          border-bottom: 1px solid var(--td-border-level-2-color);

          .preview-title {
            font-weight: 500;
            color: var(--td-text-color-primary);
          }
        }

        .preview-content {
          flex: 1;
          padding: 12px;
          overflow: auto;

          .json-display {
            margin: 0;
            padding: 12px;
            background-color: var(--td-bg-color-code);
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            line-height: 1.5;
            color: var(--td-text-color-primary);
            white-space: pre-wrap;
            word-break: break-all;
            max-height: 200px;
            overflow-y: auto;
          }
        }

        .json-error {
          margin: 12px;
        }
      }
    }
  }
}
</style>
