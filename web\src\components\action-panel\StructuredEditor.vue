<template>
  <div class="structured-editor">
    <div class="editor-layout">
      <!-- 左侧：可视化编辑器 -->
      <div class="visual-editor">
        <!-- 数组编辑器 -->
        <div v-if="dataType === 'array'" class="array-editor">
          <div class="editor-header">
            <span class="editor-title">数组编辑器</span>
            <t-button size="small" theme="primary" @click="addArrayItem">
              <template #icon><add-icon /></template>
              添加项
            </t-button>
          </div>
          <div class="array-items">
            <div v-for="(item, index) in arrayItems" :key="`array-${index}`" class="array-item">
              <div class="item-index">{{ index }}</div>
              <div class="item-input">
                <t-select
                  v-model="item.type"
                  :options="valueTypeOptions"
                  size="small"
                  style="width: 100px; margin-right: 8px"
                  @change="onArrayItemTypeChange(index)"
                />
                <t-input
                  v-if="item.type === 'string'"
                  v-model="item.value"
                  placeholder="请输入文本"
                  @change="updateArrayValue"
                />
                <t-input-number
                  v-else-if="item.type === 'number'"
                  v-model="item.value"
                  placeholder="请输入数字"
                  @change="updateArrayValue"
                />
                <t-switch v-else-if="item.type === 'boolean'" v-model="item.value" @change="updateArrayValue" />
                <t-input v-else v-model="item.value" placeholder="请输入值" @change="updateArrayValue" />
              </div>
              <t-button size="small" theme="danger" variant="text" @click="removeArrayItem(index)">
                <template #icon><delete-icon /></template>
              </t-button>
            </div>
            <div v-if="arrayItems.length === 0" class="empty-state">
              <t-button variant="dashed" block @click="addArrayItem"> 点击添加第一个数组项 </t-button>
            </div>
          </div>
        </div>

        <!-- 对象编辑器 -->
        <div v-else class="object-editor">
          <div class="editor-header">
            <span class="editor-title">对象编辑器</span>
            <t-button size="small" theme="primary" @click="addObjectProperty">
              <template #icon><add-icon /></template>
              添加属性
            </t-button>
          </div>
          <div class="object-properties">
            <div v-for="(prop, index) in objectProperties" :key="`object-${index}`" class="object-property">
              <div class="property-key">
                <t-input v-model="prop.key" placeholder="属性名" size="small" @change="updateObjectValue" />
              </div>
              <div class="property-value">
                <t-select
                  v-model="prop.type"
                  :options="valueTypeOptions"
                  size="small"
                  style="width: 100px; margin-right: 8px"
                  @change="onObjectPropertyTypeChange(index)"
                />
                <t-input
                  v-if="prop.type === 'string'"
                  v-model="prop.value"
                  placeholder="请输入文本"
                  @change="updateObjectValue"
                />
                <t-input-number
                  v-else-if="prop.type === 'number'"
                  v-model="prop.value"
                  placeholder="请输入数字"
                  @change="updateObjectValue"
                />
                <t-switch v-else-if="prop.type === 'boolean'" v-model="prop.value" @change="updateObjectValue" />
                <t-input v-else v-model="prop.value" placeholder="请输入值" @change="updateObjectValue" />
              </div>
              <t-button size="small" theme="danger" variant="text" @click="removeObjectProperty(index)">
                <template #icon><delete-icon /></template>
              </t-button>
            </div>
            <div v-if="objectProperties.length === 0" class="empty-state">
              <t-button variant="dashed" block @click="addObjectProperty"> 点击添加第一个属性 </t-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：JSON预览 -->
      <div class="json-preview">
        <div class="preview-header">
          <span class="preview-title">JSON 预览</span>
          <t-button size="small" variant="text" @click="copyJsonToClipboard">
            <template #icon><copy-icon /></template>
            复制
          </t-button>
        </div>
        <div class="preview-content">
          <pre class="json-display">{{ formattedJson }}</pre>
        </div>
        <div v-if="jsonError" class="json-error">
          <t-alert theme="error" :message="jsonError" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'StructuredEditor',
};
</script>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { AddIcon, DeleteIcon, CopyIcon } from 'tdesign-icons-vue-next';

interface Props {
  modelValue?: any;
  dataType: 'array' | 'object';
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: undefined,
});

const emits = defineEmits(['update:modelValue', 'change']);

// 结构化编辑器数据
const arrayItems = ref<Array<{ type: string; value: any }>>([]);
const objectProperties = ref<Array<{ key: string; type: string; value: any }>>([]);
const jsonError = ref('');

// 值类型选项
const valueTypeOptions = [
  { label: '文本', value: 'string' },
  { label: '数字', value: 'number' },
  { label: '布尔值', value: 'boolean' },
];

// 初始化结构化编辑器
const initializeStructuredEditor = (value: any) => {
  console.log('初始化结构化编辑器:', { dataType: props.dataType, value });

  if (props.dataType === 'array') {
    if (Array.isArray(value)) {
      arrayItems.value = value.map((item) => ({
        type: typeof item === 'number' ? 'number' : typeof item === 'boolean' ? 'boolean' : 'string',
        value: item,
      }));
    } else {
      arrayItems.value = [];
    }
  } else if (props.dataType === 'object') {
    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      objectProperties.value = Object.entries(value).map(([key, val]) => ({
        key,
        type: typeof val === 'number' ? 'number' : typeof val === 'boolean' ? 'boolean' : 'string',
        value: val,
      }));
    } else {
      objectProperties.value = [];
    }
  }
};

// 数组编辑器方法
const addArrayItem = () => {
  console.log('添加数组项');
  arrayItems.value.push({
    type: 'string',
    value: '',
  });
  updateArrayValue();
};

const removeArrayItem = (index: number) => {
  console.log('删除数组项:', index);
  arrayItems.value.splice(index, 1);
  updateArrayValue();
};

const onArrayItemTypeChange = (index: number) => {
  const item = arrayItems.value[index];
  console.log('数组项类型变化:', index, item.type);
  // 根据类型重置值
  switch (item.type) {
    case 'string':
      item.value = '';
      break;
    case 'number':
      item.value = 0;
      break;
    case 'boolean':
      item.value = false;
      break;
    default:
      item.value = '';
  }
  updateArrayValue();
};

const updateArrayValue = () => {
  const result = arrayItems.value.map((item) => {
    if (item.type === 'number') {
      return Number(item.value) || 0;
    } else if (item.type === 'boolean') {
      return Boolean(item.value);
    }
    return item.value;
  });
  console.log('更新数组值:', result);
  emits('update:modelValue', result);
  emits('change', result);
};

// 对象编辑器方法
const addObjectProperty = () => {
  console.log('添加对象属性');
  objectProperties.value.push({
    key: '',
    type: 'string',
    value: '',
  });
  updateObjectValue();
};

const removeObjectProperty = (index: number) => {
  console.log('删除对象属性:', index);
  objectProperties.value.splice(index, 1);
  updateObjectValue();
};

const onObjectPropertyTypeChange = (index: number) => {
  const prop = objectProperties.value[index];
  console.log('对象属性类型变化:', index, prop.type);
  // 根据类型重置值
  switch (prop.type) {
    case 'string':
      prop.value = '';
      break;
    case 'number':
      prop.value = 0;
      break;
    case 'boolean':
      prop.value = false;
      break;
    default:
      prop.value = '';
  }
  updateObjectValue();
};

const updateObjectValue = () => {
  const result = {};
  objectProperties.value.forEach((prop) => {
    if (prop.key) {
      if (prop.type === 'number') {
        result[prop.key] = Number(prop.value) || 0;
      } else if (prop.type === 'boolean') {
        result[prop.key] = Boolean(prop.value);
      } else {
        result[prop.key] = prop.value;
      }
    }
  });
  console.log('更新对象值:', result);
  emits('update:modelValue', result);
  emits('change', result);
};

// JSON预览相关
const formattedJson = computed(() => {
  try {
    const value =
      props.dataType === 'array'
        ? arrayItems.value.map((item) => {
            if (item.type === 'number') return Number(item.value) || 0;
            if (item.type === 'boolean') return Boolean(item.value);
            return item.value;
          })
        : (() => {
            const result = {};
            objectProperties.value.forEach((prop) => {
              if (prop.key) {
                if (prop.type === 'number') {
                  result[prop.key] = Number(prop.value) || 0;
                } else if (prop.type === 'boolean') {
                  result[prop.key] = Boolean(prop.value);
                } else {
                  result[prop.key] = prop.value;
                }
              }
            });
            return result;
          })();

    return JSON.stringify(value, null, 2);
  } catch (error) {
    return '无效的JSON';
  }
});

const copyJsonToClipboard = async () => {
  try {
    await navigator.clipboard.writeText(formattedJson.value);
    MessagePlugin.success('JSON已复制到剪贴板');
  } catch (error) {
    MessagePlugin.error('复制失败');
  }
};

// 监听外部值变化
watch(
  () => props.modelValue,
  (newValue) => {
    console.log('外部值变化:', newValue);
    initializeStructuredEditor(newValue);
  },
  { immediate: true },
);

// 监听数据类型变化
watch(
  () => props.dataType,
  () => {
    console.log('数据类型变化:', props.dataType);
    initializeStructuredEditor(props.modelValue);
  },
);
</script>

<style lang="less" scoped>
.structured-editor {
  width: 100%;

  .editor-layout {
    display: flex;
    gap: 16px;
    min-height: 300px;

    .visual-editor {
      flex: 1;
      border: 1px solid var(--td-border-level-1-color);
      border-radius: 6px;
      padding: 12px;
      background-color: var(--td-bg-color-container);

      .editor-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 1px solid var(--td-border-level-2-color);

        .editor-title {
          font-weight: 500;
          color: var(--td-text-color-primary);
        }
      }

      .array-items,
      .object-properties {
        max-height: 250px;
        overflow-y: auto;

        .array-item,
        .object-property {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 8px;
          padding: 8px;
          background-color: var(--td-bg-color-container-hover);
          border-radius: 4px;
          border: 1px solid var(--td-border-level-2-color);

          .item-index {
            min-width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: var(--td-brand-color);
            color: white;
            border-radius: 50%;
            font-size: 12px;
            font-weight: 500;
          }

          .item-input,
          .property-value {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 8px;
          }

          .property-key {
            min-width: 120px;
          }
        }

        .empty-state {
          text-align: center;
          padding: 20px;
          color: var(--td-text-color-placeholder);
        }
      }
    }

    .json-preview {
      flex: 1;
      border: 1px solid var(--td-border-level-1-color);
      border-radius: 6px;
      background-color: var(--td-bg-color-container);
      display: flex;
      flex-direction: column;

      .preview-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px;
        border-bottom: 1px solid var(--td-border-level-2-color);

        .preview-title {
          font-weight: 500;
          color: var(--td-text-color-primary);
        }
      }

      .preview-content {
        flex: 1;
        padding: 12px;
        overflow: auto;

        .json-display {
          margin: 0;
          padding: 12px;
          background-color: var(--td-bg-color-code);
          border-radius: 4px;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 12px;
          line-height: 1.5;
          color: var(--td-text-color-primary);
          white-space: pre-wrap;
          word-break: break-all;
          max-height: 200px;
          overflow-y: auto;
        }
      }

      .json-error {
        margin: 12px;
      }
    }
  }
}
</style>
