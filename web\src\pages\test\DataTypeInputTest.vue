<template>
  <div class="data-type-input-test">
    <t-card title="DataTypeInput 组件测试">
      <div class="test-container">
        <div class="test-section">
          <h3>数据类型选择</h3>
          <t-select v-model="selectedDataType" :options="dataTypeOptions" placeholder="选择数据类型" />
        </div>

        <div class="test-section">
          <h3>当前类型: {{ VALUE_TYPE_MAP[selectedDataType] || selectedDataType }}</h3>
          <data-type-input
            v-model="inputValue"
            :data-type="selectedDataType"
            :placeholder="`请输入${VALUE_TYPE_MAP[selectedDataType] || selectedDataType}`"
            @change="onValueChange"
          />
        </div>

        <div class="test-section">
          <h3>当前值</h3>
          <t-textarea
            :value="displayValue"
            readonly
            :autosize="{ minRows: 3, maxRows: 10 }"
            placeholder="当前输入的值将显示在这里"
          />
        </div>

        <div class="test-section">
          <h3>值类型</h3>
          <t-tag theme="primary">{{ typeof inputValue }}</t-tag>
        </div>
      </div>
    </t-card>
  </div>
</template>

<script lang="ts">
export default {
  name: 'DataTypeInputTest',
};
</script>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import DataTypeInput from '@/components/action-panel/DataTypeInput.vue';
import { VALUE_TYPE_MAP } from '@/components/action-panel/constants';

// 数据类型选项
const dataTypeOptions = computed(() => {
  return Object.keys(VALUE_TYPE_MAP).map((key) => ({
    label: VALUE_TYPE_MAP[key],
    value: key,
  }));
});

// 当前选择的数据类型
const selectedDataType = ref('string');

// 输入值
const inputValue = ref('');

// 显示值（格式化后的）
const displayValue = computed(() => {
  if (inputValue.value === null || inputValue.value === undefined) {
    return 'null';
  }

  if (typeof inputValue.value === 'object') {
    return JSON.stringify(inputValue.value, null, 2);
  }

  return String(inputValue.value);
});

// 值变化处理
const onValueChange = (value: any) => {
  console.log('Value changed:', value, typeof value);
};

// 监听数据类型变化，重置值
watch(
  () => selectedDataType.value,
  () => {
    inputValue.value = getDefaultValue(selectedDataType.value);
  },
);

// 获取默认值
const getDefaultValue = (dataType: string) => {
  switch (dataType) {
    case 'bool':
      return false;
    case 'int':
    case 'short':
    case 'long':
    case 'decimal':
    case 'double':
    case 'float':
      return 0;
    case 'object':
      return {};
    case 'array':
      return [];
    case 'DateTime':
      return new Date().toISOString();
    default:
      return '';
  }
};
</script>

<style lang="less" scoped>
.data-type-input-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;

  .test-container {
    .test-section {
      margin-bottom: 24px;

      h3 {
        margin-bottom: 12px;
        color: var(--td-text-color-primary);
        font-size: 16px;
        font-weight: 500;
      }
    }
  }
}
</style>
