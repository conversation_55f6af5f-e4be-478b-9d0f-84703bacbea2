<template>
  <div class="structured-editor">
    <div class="editor-layout">
      <!-- 左侧：可视化编辑器 -->
      <div class="visual-editor">
        <!-- 数组编辑器 -->
        <div v-if="dataType === 'array'" class="array-editor">
          <div class="editor-header">
            <span class="editor-title">数组编辑器</span>
            <t-button size="small" theme="primary" @click="addArrayItem">
              <template #icon><add-icon /></template>
              添加项
            </t-button>
          </div>
          <div class="array-items">
            <div v-for="(item, index) in arrayItems" :key="`array-${index}`" class="array-item">
              <div class="item-index">{{ index }}</div>
              <div class="item-input">
                <t-select
                  v-model="item.type"
                  :options="valueTypeOptions"
                  size="small"
                  style="width: 100px; margin-right: 8px"
                  @change="onArrayItemTypeChange(index)"
                />
                <!-- 基础类型 -->
                <t-input
                  v-if="item.type === 'string'"
                  v-model="item.value"
                  placeholder="请输入文本"
                  @change="updateArrayValue"
                />
                <t-input-number
                  v-else-if="item.type === 'number'"
                  v-model="item.value"
                  placeholder="请输入数字"
                  @change="updateArrayValue"
                />
                <t-switch v-else-if="item.type === 'boolean'" v-model="item.value" @change="updateArrayValue" />

                <!-- 日期时间类型 -->
                <t-date-picker
                  v-else-if="item.type === 'DateTime'"
                  v-model="item.value"
                  placeholder="请选择日期时间"
                  enable-time-picker
                  format="YYYY-MM-DD HH:mm:ss"
                  @change="updateArrayValue"
                />

                <!-- 复杂类型：对象和数组 -->
                <div v-else-if="item.type === 'object' || item.type === 'array'" class="nested-editor">
                  <t-button size="small" variant="outline" @click="openNestedEditor(index, 'array')">
                    编辑{{ item.type === 'object' ? '对象' : '数组' }}
                  </t-button>
                  <span class="nested-preview">
                    {{ getNestedPreview(item.value, item.type) }}
                  </span>
                </div>

                <!-- 默认 -->
                <t-input v-else v-model="item.value" placeholder="请输入值" @change="updateArrayValue" />
              </div>
              <t-button size="small" theme="danger" variant="text" @click="removeArrayItem(index)">
                <template #icon><delete-icon /></template>
              </t-button>
            </div>
            <div v-if="arrayItems.length === 0" class="empty-state">
              <t-button variant="dashed" block @click="addArrayItem"> 点击添加第一个数组项 </t-button>
            </div>
          </div>
        </div>

        <!-- 对象编辑器 -->
        <div v-else class="object-editor">
          <div class="editor-header">
            <span class="editor-title">对象编辑器 ({{ objectProperties.length }} 个属性)</span>
            <t-button size="small" theme="primary" @click="addObjectProperty">
              <template #icon><add-icon /></template>
              添加属性
            </t-button>
          </div>
          <div class="object-properties">
            <div v-for="(prop, index) in objectProperties" :key="`object-${index}`" class="object-property">
              <div class="property-key">
                <t-input v-model="prop.key" placeholder="属性名" size="small" @change="updateObjectValue" />
              </div>
              <div class="property-value">
                <t-select
                  v-model="prop.type"
                  :options="valueTypeOptions"
                  size="small"
                  style="width: 100px; margin-right: 8px"
                  @change="onObjectPropertyTypeChange(index)"
                />
                <!-- 基础类型 -->
                <t-input
                  v-if="prop.type === 'string'"
                  v-model="prop.value"
                  placeholder="请输入文本"
                  @change="updateObjectValue"
                />
                <t-input-number
                  v-else-if="prop.type === 'number'"
                  v-model="prop.value"
                  placeholder="请输入数字"
                  @change="updateObjectValue"
                />
                <t-switch v-else-if="prop.type === 'boolean'" v-model="prop.value" @change="updateObjectValue" />

                <!-- 日期时间类型 -->
                <t-date-picker
                  v-else-if="prop.type === 'DateTime'"
                  v-model="prop.value"
                  placeholder="请选择日期时间"
                  enable-time-picker
                  format="YYYY-MM-DD HH:mm:ss"
                  @change="updateObjectValue"
                />

                <!-- 复杂类型：对象和数组 -->
                <div v-else-if="prop.type === 'object' || prop.type === 'array'" class="nested-editor">
                  <t-button size="small" variant="outline" @click="openNestedEditor(index, 'object')">
                    编辑{{ prop.type === 'object' ? '对象' : '数组' }}
                  </t-button>
                  <span class="nested-preview">
                    {{ getNestedPreview(prop.value, prop.type) }}
                  </span>
                </div>

                <!-- 默认 -->
                <t-input v-else v-model="prop.value" placeholder="请输入值" @change="updateObjectValue" />
              </div>
              <t-button size="small" theme="danger" variant="text" @click="removeObjectProperty(index)">
                <template #icon><delete-icon /></template>
              </t-button>
            </div>
            <div v-if="objectProperties.length === 0" class="empty-state">
              <t-button variant="dashed" block @click="addObjectProperty"> 点击添加第一个属性 </t-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：JSON编辑器 -->
      <div class="json-editor">
        <div class="editor-header">
          <span class="editor-title">JSON 编辑器</span>
          <div class="editor-actions">
            <t-button size="small" variant="text" @click="formatJson">
              <template #icon><form-icon /></template>
              格式化
            </t-button>
            <t-button size="small" variant="text" @click="() => validateAndSyncJson()">
              <template #icon><check-icon /></template>
              验证同步
            </t-button>
            <t-button size="small" variant="text" @click="copyJsonToClipboard">
              <template #icon><copy-icon /></template>
              复制
            </t-button>
          </div>
        </div>
        <div class="editor-content">
          <editor
            v-model:value="jsonEditorValue"
            language="json"
            theme="light"
            :auto-wrap="true"
            :show-line-numbers="true"
            class="json-editor-monaco"
            @update:value="onJsonEditorChange"
          />
        </div>
        <div v-if="jsonError" class="json-error">
          <t-alert theme="error" :message="jsonError" />
        </div>
      </div>
    </div>

    <!-- 嵌套编辑器对话框 -->
    <t-dialog
      v-model:visible="nestedEditorVisible"
      :header="nestedEditorTitle"
      width="80%"
      :footer="false"
      @close="closeNestedEditor"
    >
      <div class="nested-editor-dialog">
        <structured-editor
          v-if="nestedEditorVisible"
          v-model="nestedEditorValue"
          :data-type="nestedEditorType"
          @change="onNestedValueChange"
        />
      </div>
    </t-dialog>
  </div>
</template>

<script lang="ts">
export default {
  name: 'StructuredEditor',
};
</script>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { AddIcon, DeleteIcon, CopyIcon, FormIcon, CheckIcon } from 'tdesign-icons-vue-next';
import Editor from '@/components/editor/index.vue';

interface Props {
  modelValue?: any;
  dataType: 'array' | 'object';
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: undefined,
});

const emits = defineEmits(['update:modelValue', 'change']);

// 结构化编辑器数据
const arrayItems = ref<Array<{ type: string; value: any }>>([]);
const objectProperties = ref<Array<{ key: string; type: string; value: any }>>([]);
const jsonError = ref('');

// 嵌套编辑器相关
const nestedEditorVisible = ref(false);
const nestedEditorValue = ref<any>(null);
const nestedEditorType = ref<'array' | 'object'>('object');
const nestedEditorTitle = ref('');
const nestedEditorContext = ref<{ type: 'array' | 'object'; index: number } | null>(null);

// JSON编辑器相关
const jsonEditorValue = ref('');
const isJsonEditorSyncing = ref(false);

// 值类型选项
const valueTypeOptions = [
  { label: '文本', value: 'string' },
  { label: '数字', value: 'number' },
  { label: '布尔值', value: 'boolean' },
  { label: '对象', value: 'object' },
  { label: '数组', value: 'array' },
  { label: '日期时间', value: 'DateTime' },
];

// 获取值的类型
const getValueType = (value: any): string => {
  if (value === null || value === undefined) {
    return 'string';
  }

  if (Array.isArray(value)) {
    return 'array';
  }

  if (typeof value === 'object') {
    // 检查是否是日期字符串
    if (typeof value === 'string' && /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(value)) {
      return 'DateTime';
    }
    return 'object';
  }

  if (typeof value === 'string') {
    // 检查是否是日期字符串
    if (/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(value)) {
      return 'DateTime';
    }
    return 'string';
  }

  return typeof value;
};

// 初始化结构化编辑器
const initializeStructuredEditor = (value: any) => {
  console.log('初始化结构化编辑器:', { dataType: props.dataType, value });

  if (props.dataType === 'array') {
    if (Array.isArray(value)) {
      arrayItems.value = value.map((item) => ({
        type: getValueType(item),
        value: item,
      }));
    } else {
      arrayItems.value = [];
    }
  } else if (props.dataType === 'object') {
    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      objectProperties.value = Object.entries(value).map(([key, val]) => ({
        key,
        type: getValueType(val),
        value: val,
      }));
    } else {
      objectProperties.value = [];
    }
  }

  // 同步到JSON编辑器
  if (!isJsonEditorSyncing.value) {
    const defaultValue = value || (props.dataType === 'array' ? [] : {});
    jsonEditorValue.value = JSON.stringify(defaultValue, null, 2);
  }
};

// 数组编辑器方法
const addArrayItem = () => {
  console.log('添加数组项');
  arrayItems.value.push({
    type: 'string',
    value: '',
  });
  updateArrayValue();
};

const removeArrayItem = (index: number) => {
  console.log('删除数组项:', index);
  arrayItems.value.splice(index, 1);
  updateArrayValue();
};

const onArrayItemTypeChange = (index: number) => {
  const item = arrayItems.value[index];
  console.log('数组项类型变化:', index, item.type);
  // 根据类型重置值
  switch (item.type) {
    case 'string':
      item.value = '';
      break;
    case 'number':
      item.value = 0;
      break;
    case 'boolean':
      item.value = false;
      break;
    case 'object':
      item.value = {};
      break;
    case 'array':
      item.value = [];
      break;
    case 'DateTime':
      item.value = new Date().toISOString();
      break;
    default:
      item.value = '';
  }
  updateArrayValue();
};

const updateArrayValue = () => {
  const result = arrayItems.value.map((item) => {
    switch (item.type) {
      case 'number':
        return Number(item.value) || 0;
      case 'boolean':
        return Boolean(item.value);
      case 'object':
        return typeof item.value === 'object' && item.value !== null ? item.value : {};
      case 'array':
        return Array.isArray(item.value) ? item.value : [];
      case 'DateTime':
        return item.value || new Date().toISOString();
      default:
        return item.value;
    }
  });
  console.log('更新数组值:', result);
  emits('update:modelValue', result);
  emits('change', result);

  // 同步到JSON编辑器
  syncVisualEditorToJson();
};

// 对象编辑器方法
const addObjectProperty = async () => {
  console.log('🔥 添加对象属性按钮被点击!');
  console.log('🔥 当前 objectProperties 长度:', objectProperties.value.length);

  const newProperty = {
    key: `property_${Date.now()}`, // 给一个默认的非空 key
    type: 'string',
    value: '',
  };

  console.log('🔥 准备添加的属性:', newProperty);

  // 使用数组替换而不是 push，确保响应式更新
  objectProperties.value = [...objectProperties.value, newProperty];

  console.log('🔥 添加后 objectProperties 长度:', objectProperties.value.length);
  console.log('🔥 添加后 objectProperties 内容:', objectProperties.value);

  // 等待 DOM 更新
  await nextTick();

  updateObjectValue();

  console.log('🔥 添加属性完成');
};

const removeObjectProperty = (index: number) => {
  console.log('删除对象属性:', index);
  // 使用数组过滤而不是 splice，确保响应式更新
  objectProperties.value = objectProperties.value.filter((_, i) => i !== index);
  updateObjectValue();
};

const onObjectPropertyTypeChange = (index: number) => {
  const prop = objectProperties.value[index];
  console.log('对象属性类型变化:', index, prop.type);
  // 根据类型重置值
  switch (prop.type) {
    case 'string':
      prop.value = '';
      break;
    case 'number':
      prop.value = 0;
      break;
    case 'boolean':
      prop.value = false;
      break;
    case 'object':
      prop.value = {};
      break;
    case 'array':
      prop.value = [];
      break;
    case 'DateTime':
      prop.value = new Date().toISOString();
      break;
    default:
      prop.value = '';
  }
  updateObjectValue();
};

const updateObjectValue = () => {
  const result = {};
  objectProperties.value.forEach((prop) => {
    if (prop.key) {
      switch (prop.type) {
        case 'number':
          result[prop.key] = Number(prop.value) || 0;
          break;
        case 'boolean':
          result[prop.key] = Boolean(prop.value);
          break;
        case 'object':
          result[prop.key] = typeof prop.value === 'object' && prop.value !== null ? prop.value : {};
          break;
        case 'array':
          result[prop.key] = Array.isArray(prop.value) ? prop.value : [];
          break;
        case 'DateTime':
          result[prop.key] = prop.value || new Date().toISOString();
          break;
        default:
          result[prop.key] = prop.value;
      }
    }
  });
  console.log('更新对象值:', result);
  emits('update:modelValue', result);
  emits('change', result);

  // 同步到JSON编辑器
  syncVisualEditorToJson();
};

const copyJsonToClipboard = async () => {
  try {
    await navigator.clipboard.writeText(jsonEditorValue.value);
    MessagePlugin.success('JSON已复制到剪贴板');
  } catch (error) {
    MessagePlugin.error('复制失败');
  }
};

// 嵌套编辑器相关方法
const getNestedPreview = (value: any, type: string) => {
  if (!value) return '空';

  if (type === 'array') {
    if (Array.isArray(value)) {
      return `数组 (${value.length} 项)`;
    }
    return '数组 (空)';
  } else if (type === 'object') {
    if (typeof value === 'object' && value !== null) {
      const keys = Object.keys(value);
      return `对象 (${keys.length} 个属性)`;
    }
    return '对象 (空)';
  }

  return JSON.stringify(value).substring(0, 20) + '...';
};

const openNestedEditor = (index: number, parentType: 'array' | 'object') => {
  console.log('🔥 打开嵌套编辑器:', { index, parentType });

  let currentValue: any;
  let itemType: string;

  if (parentType === 'array') {
    const item = arrayItems.value[index];
    currentValue = item.value;
    itemType = item.type;
    nestedEditorTitle.value = `编辑数组项 [${index}] - ${itemType === 'object' ? '对象' : '数组'}`;
  } else {
    const prop = objectProperties.value[index];
    currentValue = prop.value;
    itemType = prop.type;
    nestedEditorTitle.value = `编辑属性 "${prop.key}" - ${itemType === 'object' ? '对象' : '数组'}`;
  }

  // 设置嵌套编辑器的值和类型
  nestedEditorType.value = itemType as 'array' | 'object';
  nestedEditorValue.value = currentValue || (itemType === 'array' ? [] : {});
  nestedEditorContext.value = { type: parentType, index };
  nestedEditorVisible.value = true;
};

const closeNestedEditor = () => {
  nestedEditorVisible.value = false;
  nestedEditorValue.value = null;
  nestedEditorContext.value = null;
};

const onNestedValueChange = (value: any) => {
  console.log('🔥 嵌套值变化:', value);

  if (!nestedEditorContext.value) return;

  const { type, index } = nestedEditorContext.value;

  if (type === 'array') {
    arrayItems.value[index].value = value;
    updateArrayValue();
  } else {
    objectProperties.value[index].value = value;
    updateObjectValue();
  }
};

// JSON编辑器相关方法
const formatJson = () => {
  try {
    const parsed = JSON.parse(jsonEditorValue.value);
    jsonEditorValue.value = JSON.stringify(parsed, null, 2);
    jsonError.value = '';
    MessagePlugin.success('JSON格式化成功');
  } catch (error) {
    jsonError.value = 'JSON格式错误，无法格式化';
    MessagePlugin.error('JSON格式错误');
  }
};

// 防抖处理JSON验证和同步
let jsonValidationTimer: NodeJS.Timeout | null = null;

const onJsonEditorChange = (value: string) => {
  console.log('🔥 JSON编辑器内容变化:', value);

  // 清除之前的定时器
  if (jsonValidationTimer) {
    clearTimeout(jsonValidationTimer);
  }

  // 设置防抖，500ms后执行验证和同步
  jsonValidationTimer = setTimeout(() => {
    validateAndSyncJson(value);
  }, 500);
};

const validateAndSyncJson = (value?: string) => {
  const jsonValue = value || jsonEditorValue.value;
  console.log('🔥 验证并同步JSON:', jsonValue);

  if (!jsonValue.trim()) {
    jsonError.value = '';
    return;
  }

  try {
    const parsed = JSON.parse(jsonValue);
    jsonError.value = '';

    // 同步到可视化编辑器
    syncJsonToVisualEditor(parsed);

    // 触发外部更新
    emits('update:modelValue', parsed);
    emits('change', parsed);

    console.log('🔥 JSON同步成功:', parsed);
  } catch (error) {
    jsonError.value = 'JSON格式错误，请检查语法';
    console.error('🔥 JSON解析错误:', error);
  }
};

const syncJsonToVisualEditor = (value: any) => {
  console.log('🔥 同步JSON到可视化编辑器:', value);
  isJsonEditorSyncing.value = true;

  try {
    initializeStructuredEditor(value);
  } finally {
    isJsonEditorSyncing.value = false;
  }
};

const syncVisualEditorToJson = () => {
  if (isJsonEditorSyncing.value) return;

  const currentValue = getCurrentValue();
  const formattedJson = JSON.stringify(currentValue, null, 2);

  if (jsonEditorValue.value !== formattedJson) {
    jsonEditorValue.value = formattedJson;
    console.log('🔥 同步可视化编辑器到JSON:', currentValue);
  }
};

const getCurrentValue = () => {
  if (props.dataType === 'array') {
    return arrayItems.value.map((item) => {
      switch (item.type) {
        case 'number':
          return Number(item.value) || 0;
        case 'boolean':
          return Boolean(item.value);
        case 'object':
          return typeof item.value === 'object' && item.value !== null ? item.value : {};
        case 'array':
          return Array.isArray(item.value) ? item.value : [];
        case 'DateTime':
          return item.value || new Date().toISOString();
        default:
          return item.value;
      }
    });
  } else {
    const result = {};
    objectProperties.value.forEach((prop) => {
      if (prop.key) {
        switch (prop.type) {
          case 'number':
            result[prop.key] = Number(prop.value) || 0;
            break;
          case 'boolean':
            result[prop.key] = Boolean(prop.value);
            break;
          case 'object':
            result[prop.key] = typeof prop.value === 'object' && prop.value !== null ? prop.value : {};
            break;
          case 'array':
            result[prop.key] = Array.isArray(prop.value) ? prop.value : [];
            break;
          case 'DateTime':
            result[prop.key] = prop.value || new Date().toISOString();
            break;
          default:
            result[prop.key] = prop.value;
        }
      }
    });
    return result;
  }
};

// 监听外部值变化
watch(
  () => props.modelValue,
  (newValue) => {
    console.log('外部值变化:', newValue);
    initializeStructuredEditor(newValue);
  },
  { immediate: true },
);

// 监听数据类型变化
watch(
  () => props.dataType,
  () => {
    console.log('数据类型变化:', props.dataType);
    initializeStructuredEditor(props.modelValue);
  },
);
</script>

<style lang="less" scoped>
.structured-editor {
  width: 100%;

  .editor-layout {
    display: flex;
    gap: 16px;
    min-height: 300px;

    .visual-editor {
      flex: 1;
      border: 1px solid var(--td-border-level-1-color);
      border-radius: 6px;
      padding: 12px;
      background-color: var(--td-bg-color-container);

      .editor-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 1px solid var(--td-border-level-2-color);

        .editor-title {
          font-weight: 500;
          color: var(--td-text-color-primary);
        }
      }

      .array-items,
      .object-properties {
        max-height: 250px;
        overflow-y: auto;

        .array-item,
        .object-property {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 8px;
          padding: 8px;
          background-color: var(--td-bg-color-container-hover);
          border-radius: 4px;
          border: 1px solid var(--td-border-level-2-color);

          .item-index {
            min-width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: var(--td-brand-color);
            color: white;
            border-radius: 50%;
            font-size: 12px;
            font-weight: 500;
          }

          .item-input,
          .property-value {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 8px;
          }

          .property-key {
            min-width: 120px;
          }
        }

        .empty-state {
          text-align: center;
          padding: 20px;
          color: var(--td-text-color-placeholder);
        }

        .nested-editor {
          display: flex;
          align-items: center;
          gap: 8px;
          flex: 1;

          .nested-preview {
            font-size: 12px;
            color: var(--td-text-color-placeholder);
            font-style: italic;
          }
        }
      }
    }

    .json-editor {
      flex: 1;
      border: 1px solid var(--td-border-level-1-color);
      border-radius: 6px;
      background-color: var(--td-bg-color-container);
      display: flex;
      flex-direction: column;

      .editor-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px;
        border-bottom: 1px solid var(--td-border-level-2-color);

        .editor-title {
          font-weight: 500;
          color: var(--td-text-color-primary);
        }

        .editor-actions {
          display: flex;
          gap: 8px;
        }
      }

      .editor-content {
        flex: 1;

        .json-editor-monaco {
          height: 400px;
          overflow: hidden;
        }
      }

      .json-error {
        margin: 12px;
      }
    }
  }

  .nested-editor-dialog {
    // min-height: 400px;

    :deep(.structured-editor) {
      .editor-layout {
        min-height: 350px;
      }
    }
  }
}
</style>
